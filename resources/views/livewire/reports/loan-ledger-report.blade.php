<div>
    <!-- Date Filters -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-end gap-2 my-4">
                <x-date-filter/>
                <x-export-buttons :with-excel="true"/>
            </div>
        </div>
    </div>

    <!-- Ledger Table -->
    @if($records->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                <tr>
                    <th>Date</th>
                    <th>Description</th>
                    <th class="text-end">Amount</th>
                </tr>
                </thead>
                <tbody>

                @foreach($records as $index => $entry)
                    <tr>
                        <td>{{ $entry->created_at->format('d M Y H:i:s') }}</td>
                        <td>{{ $entry->entry_type }}</td>
                        <td class="text-end">{{ $entry->amount }}</td>
                    </tr>
                @endforeach
                </tbody>
                <tfoot class="table-light">
                <tr>
                    <th colspan="2">Total</th>
                    <th class="text-end">
                        {{ number_format($records->sum('debit_amount'), 2) }}
                    </th>
                </tr>
                </tfoot>
            </table>
        </div>

        <!-- Pagination -->
        <div class="card-footer">
            {{ $records->links() }}
        </div>
    @else
        <div class="text-center py-5">
            <i class="bx bx-receipt bx-lg text-muted"></i>
            <h6 class="mt-2">No Transactions Found</h6>
            <p class="text-muted">
                @if($startDate || $endDate)
                    No transactions found for the selected date range.
                @else
                    No transactions have been recorded for this loan account yet.
                @endif
            </p>
        </div>
    @endif
</div>
