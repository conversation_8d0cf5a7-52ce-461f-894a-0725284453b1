<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Traits\NotifyWhen;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class SmsNotification extends Notification implements ShouldQueue
{
    use Queueable, NotifyWhen;

    protected $message;
    protected $phoneNumber;
    public $partnerID;
    protected $customerID;

    public function __construct($message, $phoneNumber, $customerID, $partnerID)
    {
        $this->message = $message;
        $this->phoneNumber = $phoneNumber;
        $this->partnerID = $partnerID;
        $this->customerID = $customerID;
    }

    public function via(object $notifiable): array
    {
        if ($this->hasOldTransaction($notifiable)) {
            return [];
        }

        return ['sms', 'database'];
    }

    public function toDatabase($notifiable): array
    {
        return [
            'message' => $this->message,
            'phoneNumber' => $this->phoneNumber,
            'partnerID' => $this->partnerID,
            'customerID' => $this->customerID
        ];
    }

    public function toSms($notifiable)
    {
        return $this->message;
    }

    public function shouldSend(object $notifiable, string $channel): bool
    {
        return true;
    }

    private function hasOldTransaction(object $notifiable): bool
    {
        $transaction = Transaction::query()
            ->where('Telephone_Number', $notifiable->Telephone_Number)
            ->latest()
            ->first();

        return ((int) now()->diffInDays($transaction->created_at, true)) > 1;
    }
}
