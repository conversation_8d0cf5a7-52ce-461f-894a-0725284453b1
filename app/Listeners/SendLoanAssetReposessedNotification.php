<?php

namespace App\Listeners;

use App\Models\Partner;
use App\Events\LoanAssetReposessed;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Mail\LoanAssetReposessed as LoanAssetReposessedMail;
use App\Models\Customer;
use App\Models\CustomerAsset;
use App\Notifications\SmsNotification;
use Illuminate\Support\Facades\Log;

class SendLoanAssetReposessedNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LoanAssetReposessed $event): void
    {

        $partner = Partner::query()->firstWhere('Institution_Name', 'like', '%spiro%');

        if (app()->isProduction() == false) {
            $this->sendSmsToClient($event->asset, $partner->id);
            return;
        }

        if (empty($recipients = $partner?->Email_Notification_Recipients)) {
            return;
        }

        $recipients = array_map('trim', explode(',', $recipients));

        Mail::to($recipients)->send(new LoanAssetReposessedMail($event->asset));
        $this->sendToBank($event);
    }

    protected function sendToBank(LoanAssetReposessed $event)
    {
        $partner = Partner::query()->firstWhere('Institution_Name', 'like', '%dfcu%');

        if (empty($recipients = $partner?->Email_Notification_Recipients)) {
            return;
        }

        Mail::to($recipients)->send(new LoanAssetReposessedMail($event->asset));
        $this->sendSmsToClient($event->asset, $partner->id);
    }

    protected function sendSmsToClient(CustomerAsset $asset, int $partnerId)
    {
        $customer = $asset->customer;
        $customer->notify(new SmsNotification('Hello ' . $customer->name . ', please be notified that your bike ' . $asset->Identification . 'has been successfully reposessed.', $customer->Telephone_Number, $customer->id, $partnerId));
    }
}
