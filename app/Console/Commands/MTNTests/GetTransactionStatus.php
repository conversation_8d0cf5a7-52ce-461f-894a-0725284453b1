<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\Transaction;
use App\Services\MockMtnApiService;
use App\Services\MtnApiService;
use App\Exceptions\MtnApiException;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class GetTransactionStatus extends Command
{
    protected $signature = 'mtn:transaction
                          {reference : Transaction reference ID}';

    protected $description = 'Test MTN get transaction status API';

    public function handle(): int
    {
        try {
            $reference = $this->argument('reference');
            $transaction = Transaction::query()->firstWhere('TXN_ID', $reference);

            $this->info("Checking transaction status...");
            $this->line("Reference: $reference");

            $mtnService = (new PaymentServiceManager($transaction))->paymentService;
            $response = $mtnService->collectionStatus($reference);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());

            return 1;
        }
    }
}
