<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\MockMtnApiService;
use App\Exceptions\MtnApiException;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class UnlinkLoan extends Command
{
    protected $signature = 'mtn:unlink-loan {applicationId} {--phone=}';
    protected $description = 'Test MTN collect money from agent API';

    public function handle(): int
    {
        try {
            $transaction = Transaction::query()->firstWhere([
                'Loan_Application_ID' => $this->argument('applicationId'),
            ]);

            if (empty($transaction)) {
                $this->error('Transaction not found');
                return 1;
            }

            $api = (new PaymentServiceManager($transaction))->paymentService;
            $response = $api->closeLoan($transaction);

            $this->info((string) $response);

            if ($response) {
                $this->info('Loan unlinked successfully');

                return 0;
            }

            $this->info('Failed to unlink loan');
            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());

            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
