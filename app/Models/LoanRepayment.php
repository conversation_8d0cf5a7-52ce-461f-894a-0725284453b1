<?php

namespace App\Models;

use App\Enums\AccountClosureReason;
use App\Enums\LoanAccountType;
use Exception;
use Carbon\Carbon;
use App\Models\LoanFee;
use App\Models\Accounts\Account;
use App\Models\Scopes\PartnerScope;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Services\Account\AccountSeederService;
use App\Models\Transactables\Contracts\Transactable;
use App\Notifications\SmsNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LoanRepayment extends Model implements Transactable
{
    use HasFactory, SoftDeletes;

    // Constants for account status codes
    const STATUS_OUTSTANDING_BEYOND_TERMS = 1;
    const STATUS_RESTRUCTURED = 2;
    const STATUS_WRITTEN_OFF = 3;
    const STATUS_FULLY_PAID = 4;
    const STATUS_CURRENT_WITHIN_TERMS = 5;
    const STATUS_WRITTEN_OFF_RECOVERY = 6;
    const STATUS_FORFEITURE = 7;

    protected $fillable = [
        "Loan_ID",
        "Customer_ID",
        "Partner_ID",
        'amount',
        "Transaction_Date",
        "Current_Balance_Amount",
        "Current_Balance_Amount_UGX_Equivalent",
        "Current_Balance_Indicator",
        "Last_Payment_Date",
        "Last_Payment_Amount",
        "Credit_Account_Status",
        "Last_Status_Change_Date",
        "Credit_Account_Risk_Classification",
        "Credit_Account_Arrears_Date",
        "Number_of_Days_in_Arrears",
        "Balance_Overdue",
        "Risk_Classification_Criteria",
        "Opening_Balance_Indicator ",
        "Annual_Interest_Rate_at_Reporting",
        "partner_notified",
        "partner_notified_date"
    ];

    protected $dates = [
        'Credit_Account_Arrears_Date',
        'Last_Status_Change_Date',
        'Last_Payment_Date',
        'Transaction_Date'
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);

        static::created(function ($loanPayment) {
            $loanPayment->sendPaymentNotification();
        });
    }

    /**
     * Create a new loan payment
     */
    public static function createPayment(Loan $loan, float $amount): self
    {
        $accountStatus = self::determineAccountStatus($loan);

        return self::query()->create([
            "Loan_ID" => $loan->id,
            "Customer_ID" => $loan->Customer_ID,
            "Partner_ID" => $loan->Partner_ID,
            "amount" => $amount,
            "Transaction_Date" => Carbon::now(),
            "Last_Payment_Date" => Carbon::now(),
            "Last_Payment_Amount" => $amount,
            "Credit_Account_Status" => $accountStatus,
            'Current_Balance_Amount' => $loan->totalOutstandingBalance() - $amount,
            'Current_Balance_Amount_UGX_Equivalent' => $loan->totalOutstandingBalance() - $amount,
        ]);
    }

    /**
     * Determine the account status based on loan state
     */
    private static function determineAccountStatus(Loan $loan): int
    {
        /**
         * We are checking for written off first because
         * this is marked as a recovery other than the usual repayment
         */
        if ($loan->isWrittenOff()) {
            return self::STATUS_WRITTEN_OFF;
        }

        if ($loan->isOverdue()) {
            return self::STATUS_OUTSTANDING_BEYOND_TERMS;
        }

        if ($loan->isCleared()) {
            return self::STATUS_FULLY_PAID;
        }

        return self::STATUS_CURRENT_WITHIN_TERMS;
    }

    /**
     * Send payment notification to customer
     */
    private function sendPaymentNotification(): void
    {
        $loanProductName = $this->loan->loan_product->Name;

        if ($this->Current_Balance_Amount == 0) {
            $message = "PAID UGX {$this->amount} to {$loanProductName}. Your loan is now fully paid, and your outstanding balance is UGX 0";
        } else {
            $amount = round($this->Current_Balance_Amount);
            $message = "PAID UGX {$this->amount} to {$loanProductName}. Your outstanding balance is UGX {$amount}";
        }

        $this->customer->notify(new SmsNotification(
            $message,
            $this->customer->Telephone_Number,
            $this->customer->id,
            $this->Partner_ID
        ));
    }

    // Relationships
    public function loan()
    {
        return $this->belongsTo(Loan::class, 'Loan_ID');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, "Customer_ID");
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class, "Partner_ID");
    }

    public function journalEntries(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(JournalEntry::class, 'journable', 'transactable', 'transactable_id')->chaperone();
    }

    public function amount(): float
    {
        return $this->amount;
    }

    /**
     * Process the payment and affect relevant accounts
     */
    public function affectAccounts(): void
    {
        DB::beginTransaction();

        try {
            $this->processPayment();
            DB::commit();
        } catch (Exception $e) {
            Log::error($e->getMessage());
            DB::rollBack();
        }
    }

    /**
     * Main payment processing logic
     */
    private function processPayment(): void
    {
        $txnId = $this->generateTransactionId();
        $accounts = $this->getRequiredAccounts();
        $paymentAmount = $this->amount();
        $repaymentOrder = $this->getRepaymentOrder();

        $transactions = [];
        $loanSchedules = $this->getLoanSchedules();

        foreach ($loanSchedules as $schedule) {
            if ($paymentAmount <= 0) break;

            $allCleared = true;

            foreach ($repaymentOrder as $payable) {
                if ($paymentAmount <= 0) break;

                $this->processPayable(
                    $schedule,
                    $payable,
                    $accounts,
                    $transactions,
                    $txnId,
                    $paymentAmount,
                    $allCleared
                );
            }

            if (!$allCleared) break;
        }

        $this->recordTransactions($transactions);
        $this->updateLoanStatus();
    }

    /**
     * Process a single payable item for a schedule
     */
    private function processPayable(
        LoanSchedule $schedule,
        string $payable,
        array $accounts,
        array &$transactions,
        string $txnId,
        float &$paymentAmount,
        bool &$allCleared
    ): void {
        while (($outstandingAmount = $this->getOutstandingAmountForSchedule($schedule, $payable)) > 0) {
            $amountToPay = min($paymentAmount, $outstandingAmount);

            if ($amountToPay <= 0) break;

            $this->handlePayment(
                $schedule,
                $payable,
                $accounts,
                $transactions,
                $txnId,
                $amountToPay
            );

            if ($this->shouldDeductPaymentAmount($schedule, $payable)) {
                $paymentAmount -= $amountToPay;
            }

            if ($this->getOutstandingAmountForSchedule($schedule, $payable) > 0) {
                $allCleared = false;
            }

            if ($paymentAmount <= 0) break;
        }
    }

    /**
     * Handle payment accounting and application
     */
    private function handlePayment(
        LoanSchedule $schedule,
        string $payable,
        array $accounts,
        array &$transactions,
        string $txnId,
        float $amountToPay
    ): void {
        // Normalize payable type for fees
        $payableType = str_contains($schedule->type, 'Fee') ? 'Fees' : $payable;

        $this->processAccounting(
            $schedule->payable_to,
            $payableType,
            $schedule->type,
            $accounts,
            $amountToPay,
            $txnId,
            $transactions
        );

        $this->applyPaymentToSchedule($schedule, $payableType, $amountToPay);
    }

    /**
     * Get required accounts for transaction processing
     */
    private function getRequiredAccounts(): array
    {
        $loanProduct = $this->loan->loan_product;

        return [
            'interest' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::INTEREST_INCOME_FROM_LOANS_SLUG)
                ->first(),
            'interest-receivable' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::INTEREST_RECEIVABLES_SLUG)
                ->first(),
            'penalties-receivable' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::PENALTIES_RECEIVABLES_SLUG)
                ->first(),
            'penalties' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::PENALTIES_FROM_LOAN_PAYMENTS_SLUG)
                ->first(),
            'fees' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::INCOME_FROM_FINES_SLUG)
                ->first(),
            'loan_product' => $loanProduct->general_ledger_account,
            'collection_ova' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::COLLECTION_OVA_SLUG)
                ->first(),
            'permit' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::PERMIT_RECEIVABLES_SLUG)
                ->first(),
            'insurance' => Account::where("partner_id", $loanProduct->Partner_ID)
                ->where("slug", AccountSeederService::INSURANCE_RECEIVABLES_SLUG)
                ->first(),
        ];
    }

    /**
     * Get the repayment order from loan product
     */
    private function getRepaymentOrder(): array
    {
        $repaymentOrder = $this->loan->loan_product->Repayment_Order;

        // If it's a string, try to decode it as JSON
        if (is_string($repaymentOrder)) {
            $repaymentOrder = json_decode($repaymentOrder, true);
        }

        // If it's not an array (either because decoding failed or original wasn't an array), use default
        return is_array($repaymentOrder) ? $repaymentOrder : ['Principal', 'Interest'];
    }

    /**
     * Get loan schedules ordered by installment number
     */
    private function getLoanSchedules()
    {
        return LoanSchedule::where('loan_id', $this->loan->id)
            ->orderBy('installment_number')
            ->get();
    }

    /**
     * Generate a transaction ID
     */
    private function generateTransactionId(): string
    {
        return rand(11111, 99999) . "-" . now()->unix();
    }

    /**
     * Record all transactions
     */
    private function recordTransactions(array $transactions): void
    {
        JournalEntry::insert($transactions);
    }

    /**
     * Update loan status after payment
     */
    private function updateLoanStatus(): void
    {
        $loan = $this->loan;
        $accountStatus = $this->determineAccountStatus($loan);

        $loan->Credit_Account_Status = $accountStatus;
        $loan->Last_Status_Change_Date = Carbon::now();

        if ($accountStatus == self::STATUS_FULLY_PAID) {
            $loan->Credit_Account_Closure_Date = Carbon::now();
            $loan->Credit_Account_Closure_Reason = "Loan has been fully paid";
        }

        if ($accountStatus == self::STATUS_WRITTEN_OFF) {
            $loan->Credit_Account_Closure_Date = Carbon::now();
            $loan->Credit_Account_Closure_Reason = AccountClosureReason::WrittenOff->value;
        }

        $this->Credit_Account_Status = $accountStatus;

        $loan->save();
        $this->save();
    }

    /**
     * Get outstanding amount for a schedule item
     */
    private function getOutstandingAmountForSchedule(LoanSchedule $schedule, string $payable): float
    {
        if (str_contains($schedule->type, 'Fee')) {
            if ($this->shouldProcessSchedule($schedule)) {
                return $schedule->total_outstanding;
            }
            return 0;
        }

        switch ($payable) {
            case 'Fees':
                return $this->shouldProcessSchedule($schedule) ? $schedule->total_outstanding : 0;
            case 'Interest':
                return $this->shouldProcessSchedule($schedule) ? $schedule->interest_remaining : 0;
            case 'Principal':
                return $schedule->principal_remaining;
            case 'Penalty':
                return $this->loan->getOutstandingPenalties();
            default:
                return 0;
        }
    }

    /**
     * Check if a schedule should be processed
     */
    private function shouldProcessSchedule(LoanSchedule $schedule): bool
    {
        return $schedule->payment_due_date <= now() || $schedule->installment_number == 1;
    }

    /**
     * Check if payment amount should be deducted
     */
    private function shouldDeductPaymentAmount(LoanSchedule $schedule, string $payable): bool
    {
        return $schedule->payment_due_date <= now() || $payable == 'Principal' || $schedule->installment_number == 1;
    }

    /**
     * Apply payment to a schedule item
     */
    private function applyPaymentToSchedule(LoanSchedule $schedule, string $payable, float $amount): void
    {
        switch ($payable) {
            case 'Interest':
                $this->applyInterestPayment($schedule, $amount);
                break;
            case 'Principal':
                $schedule->principal_remaining = max(0, $schedule->principal_remaining - $amount);
                break;
            case 'Fees':
                $schedule->total_outstanding = max(0, $schedule->total_outstanding - $amount);
                break;
        }

        $this->updateScheduleBalance($schedule, $payable);
    }

    /**
     * Apply interest payment with special handling for recurring loans
     */
    private function applyInterestPayment(LoanSchedule $schedule, float $amount): void
    {
        if ($schedule->payment_due_date >= now() && $schedule->loan->isRecurring()) {
            $schedule->interest_remaining = 0;
        } else {
            $schedule->interest_remaining = max(0, $schedule->interest_remaining - $amount);
        }
    }

    /**
     * Update schedule balance after payment
     */
    private function updateScheduleBalance(LoanSchedule $schedule, string $payable): void
    {
        $remainingBalance = ($payable == 'Fees')
            ? $schedule->total_outstanding
            : $schedule->principal_remaining + $schedule->interest_remaining;

        $schedule->total_outstanding = max(0, $remainingBalance);
        $schedule->save();
    }

    /**
     * Process accounting for a payment
     */
    private function processAccounting(
        $payableTo,
        string $payable,
        string $type,
        array $accounts,
        float $amount,
        string $txnId,
        array &$transactions
    ): void {
        $accountingDetails = $this->getAccountingDetails($payableTo, $payable, $type, $accounts, $amount);
        if ($accountingDetails['gla']) {
            $this->recordCollectionOvaEntry($accounts, $amount, $txnId, $transactions);

            if ($this->shouldHandleAccrualInterest($accountingDetails['gla'], $accounts)) {
                $this->handleAccrualInterest($accounts, $amount, $txnId, $transactions, $accountingDetails);
            } elseif ($this->shouldHandleAccrualPenalties($accountingDetails['gla'], $accounts)) {
                $this->handleAccrualPenalties($accounts, $amount, $txnId, $transactions, $accountingDetails);
            } else {
                $this->recordGeneralLedgerEntry(
                    $accountingDetails['gla'],
                    $amount,
                    $txnId,
                    $transactions,
                    $accountingDetails['accounting_type'],
                    $accountingDetails['cash_type'],
                    $accountingDetails['debit_amount'],
                    $accountingDetails['credit_amount']
                );
            }
        }

        $this->updateFeeOrPenaltyStatus($payable, $type, $amount);
    }

    /**
     * Get accounting details for a payable type
     */
    private function getAccountingDetails($payableTo, string $payable, string $type, array $accounts, float $amount): array
    {
        $details = [
            'gla' => null,
            'cash_type' => "Cash In",
            'accounting_type' => 'Credit',
            'debit_amount' => 0,
            'credit_amount' => $amount,
        ];

        switch ($payable) {
            case 'Fees':
                $details['gla'] = $payableTo ? Account::find($payableTo) : $accounts['fees'];
                break;
            case 'Interest':
                $details['gla'] = $accounts['interest'];
                break;
            case 'Penalty':
                $details['gla'] = $accounts['penalties'];
                break;
            case 'Principal':
                $details = $this->getPrincipalAccountingDetails($type, $accounts, $amount);
                break;
        }

        return $details;
    }

    /**
     * Get accounting details for principal payments
     */
    private function getPrincipalAccountingDetails(string $type, array $accounts, float $amount): array
    {
        $details = [
            'gla' => $accounts['loan_product'],
            'cash_type' => "Cash In",
            'accounting_type' => 'Credit',
            'debit_amount' => 0,
            'credit_amount' => $amount,
        ];

        if ($this->loan->isAssetLoan()) {
            if ($type == 'Loan') {
                $details['gla'] = $accounts['loan_product'];
            } elseif (Str::contains(strtolower($type), 'insurance')) {
                $details['gla'] = $accounts['insurance'];
            } elseif (Str::contains(strtolower($type), 'permit')) {
                $details['gla'] = $accounts['permit'];
            }
        }

        return $details;
    }

    /**
     * Record collection OVA entry
     */
    private function recordCollectionOvaEntry(array $accounts, float $amount, string $txnId, array &$transactions): void
    {
        $this->recordGeneralLedgerEntry(
            $accounts['collection_ova'],
            $amount,
            $txnId,
            $transactions,
            "Debit",
            "Cash In",
            $amount,
            0
        );
    }

    /**
     * Check if accrual interest should be handled
     */
    private function shouldHandleAccrualInterest(?Account $gla, array $accounts): bool
    {
        return $gla == $accounts['interest'] && $this->partner->Accounting_Type == 'Accrual';
    }

    /**
     * Check if accrual interest should be handled
     */
    private function shouldHandleAccrualPenalties(?Account $gla, array $accounts): bool
    {
        return $gla == $accounts['penalties'] && $this->partner->Accounting_Type == 'Accrual';
    }

    /**
     * Handle accrual interest accounting
     */
    private function handleAccrualInterest(array $accounts, float $amount, string $txnId, array &$transactions, array $accountingDetails): void
    {
        $gla = $accounts['interest-receivable'];
        $accruedInterest = $this->getAccruedInterest($gla);

        $this->recordGeneralLedgerEntry(
            $gla,
            $accruedInterest,
            $txnId,
            $transactions,
            "Credit",
            "Cash In",
            0,
            $accruedInterest
        );

        $remainingInterest = $amount - $accruedInterest;

        if ($remainingInterest > 0) {
            $this->recordGeneralLedgerEntry(
                $accounts['interest'],
                $remainingInterest,
                $txnId,
                $transactions,
                $accountingDetails['accounting_type'],
                $accountingDetails['cash_type'],
                0,
                $remainingInterest
            );
        }
    }

    /**
     * Handle accrual interest accounting
     */
    private function handleAccrualPenalties(array $accounts, float $amount, string $txnId, array &$transactions, array $accountingDetails): void
    {
        $gla = $accounts['penalties-receivable'];
        $accruedPenalties = $this->getAccruedPenalties($gla);

        $this->recordGeneralLedgerEntry(
            $gla,
            $accruedPenalties,
            $txnId,
            $transactions,
            "Credit",
            "Cash In",
            0,
            $accruedPenalties
        );
    }

    /**
     * Get accrued interest for the loan
     */
    private function getAccruedInterest(Account $gla): float
    {
        return JournalEntry::where('customer_id', $this->Customer_ID)
            ->where('account_id', $gla->id)
            ->where('accounting_type', 'Debit')
            ->sum('amount');
    }

    /**
     * Get accrued interest for the loan
     */
    private function getAccruedPenalties(Account $gla): float
    {
        return JournalEntry::where('customer_id', $this->Customer_ID)
            ->where('account_id', $gla->id)
            ->where('accounting_type', 'Debit')
            ->sum('amount');
    }

    /**
     * Record a general ledger entry
     */
    private function recordGeneralLedgerEntry(
        Account $account,
        float $amount,
        string $txnId,
        array &$transactions,
        string $accountingType,
        string $cashType,
        float $debitAmount,
        float $creditAmount
    ): void {
        $previousBalance = $account->balance;

        // Update account balance
        if ($account->type_letter == 'A' || $account->type_letter == 'E') {
            $account->balance = $accountingType === 'Credit'
                ? $previousBalance - $amount
                : $previousBalance + $amount;
        } else {
            $account->balance = $accountingType === 'Credit'
                ? $previousBalance + $amount
                : $previousBalance - $amount;
        }

        $account->save();

        // Record transaction
        $transactions[] = [
            "previous_balance" => $previousBalance,
            "current_balance" => $account->balance,
            "transactable" => self::class,
            'accounting_type' => $accountingType,
            "customer_id" => $this->Customer_ID,
            'credit_amount' => $creditAmount,
            "partner_id" => $this->Partner_ID,
            "account_name" => $account->name,
            'debit_amount' => $debitAmount,
            "transactable_id" => $this->id,
            "account_id" => $account->id,
            "cash_type" => $cashType,
            "created_at" => now(),
            "updated_at" => now(),
            "amount" => $amount,
            "txn_id" => $txnId,
        ];
    }

    /**
     * Update fee or penalty status after payment
     */
    private function updateFeeOrPenaltyStatus(string $payable, string $type, float $amount): void
    {
        $loan = $this->loan;

        if ($payable === "Fees") {
            $this->updateFeeStatus($loan, $type, $amount);
        } elseif ($payable === "Penalty") {
            $this->updatePenaltyStatus($loan, $amount);
        }
    }

    /**
     * Update fee status after payment
     */
    private function updateFeeStatus(Loan $loan, string $type, float $amount): void
    {
        $fees = $loan->fees->where('Charge_At', "Repayment")->where('Status', '!=', LoanFee::FULLY_PAID);

        foreach ($fees as $fee) {
            if ($fee->loan_product_fee->Name == $type) {
                $amountToPay = min($fee->Amount_To_Pay, $amount);
                $fee->Amount = $amountToPay;
                $fee->Status = $this->determineFeeStatus($fee);
                $fee->save();
            }
        }
    }

    /**
     * Update penalty status after payment
     */
    private function updatePenaltyStatus(Loan $loan, float $amount): void
    {
        $penalties = $loan->penalties->where('Status', '!=', LoanFee::FULLY_PAID);

        foreach ($penalties as $penalty) {
            $amountToPay = min($penalty->Amount_To_Pay, $amount);
            $penalty->Amount = $amountToPay;
            $penalty->Status = $this->determinePenaltyStatus($penalty);
            $penalty->save();
        }
    }

    /**
     * Determine fee status based on payment
     */
    private function determineFeeStatus(LoanFee $fee): string
    {
        if ($fee->Amount == $fee->Amount_To_Pay) {
            return LoanFee::FULLY_PAID;
        }
        if ($fee->Amount > 0 && $fee->Amount < $fee->Amount_To_Pay) {
            return LoanFee::PARTIALLY_PAID;
        }
        return $fee->Status;
    }

    /**
     * Determine penalty status based on payment
     */
    private function determinePenaltyStatus(LoanPenalty $penalty): string
    {
        if ($penalty->Amount == $penalty->Amount_To_Pay) {
            return LoanPenalty::FULLY_PAID;
        }
        if ($penalty->Amount > 0 && $penalty->Amount < $penalty->Amount_To_Pay) {
            return LoanPenalty::PARTIALLY_PAID;
        }
        return $penalty->Status;
    }
}
