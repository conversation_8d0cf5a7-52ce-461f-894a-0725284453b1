<?php

namespace App\Actions\Reports;

use App\Http\Controllers\pages\AccountSettingsAccount;
use App\Models\JournalEntry;
use App\Models\Loan;
use App\Models\LoanDisbursement;
use App\Models\LoanProduct;
use App\Models\LoanRepayment;
use App\Models\LoanPenalty;
use App\Models\LoanFee;
use App\Services\Account\AccountSeederService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class GetLoanLedgerReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected int $loanId = 0;
    protected int $perPage = 0;

    public function execute()
    {
        $loan = Loan::query()->find($this->loanId);

        if (!$loan) {
            return collect();
        }

//        $query = DB::table('loan_schedules')
////            ->selectRaw('
////   *
////            ')
//            ->join('journal_entries', 'journal_entries.customer_id', '=', $loan->Customer_ID)
//            ->where('loan_id', $loan->id)
//            ->where('journal_entries.transactable', 'LIKE', '%LoanSchedule%')
//            ->select('journal_entries.*');

//
//        $query = JournalEntry::query()
//            ->with(['account', 'customer', 'journable'])
//            ->where(function ($query) use ($loan) {
//                // Get entries where customer_id matches the loan's customer
//                $query->where('customer_id', $loan->Customer_ID);
//                // Additional filtering to ensure we get entries related to this specific loan
//                $query->where(function ($subQuery) use ($loan) {
//                    // For loan disbursements
//                    $subQuery->whereHasMorph('journable', [LoanDisbursement::class], function ($q) use ($loan) {
//                        $q->where('loan_id', $loan->id);
//                    })
//                    // For loan repayments
//                    ->orWhereHasMorph('journable', [LoanRepayment::class], function ($q) use ($loan) {
//                        $q->where('Loan_ID', $loan->id);
//                    })
//                    // For loan penalties
//                    ->orWhereHasMorph('journable', [LoanPenalty::class], function ($q) use ($loan) {
//                        $q->where('Loan_ID', $loan->id);
//                    });
//
//                    // Add LoanFee if it exists
//                    if (class_exists('App\Models\LoanFee')) {
//                        $subQuery->orWhereHasMorph('journable', ['App\Models\LoanFee'], function ($q) use ($loan) {
//                            $q->where('loan_id', $loan->id);
//                        });
//                    }
//                });
//            });

        // Apply date filters if provided
        if ($this->startDate && $this->endDate) {
            $query->whereBetween('created_at', [
                Carbon::parse($this->startDate)->startOfDay()->toDateTimeString(),
                Carbon::parse($this->endDate)->endOfDay()->toDateTimeString(),
            ]);
        }

        // Order by date (oldest first to show chronological transaction history)
        $query->orderBy('created_at', 'asc');

        if ($this->perPage > 0) {
            return $query->paginate($this->perPage);
        }

        return $query->get();
    }

    public function paginate($perPage = 100): self
    {
        $this->perPage = $perPage;
        return $this;
    }

    public function filters(array $details): self
    {
        $this->startDate = Arr::get($details, 'startDate', '');
        $this->endDate = Arr::get($details, 'endDate', '');
        $this->loanId = Arr::get($details, 'loanId', 0);

        // Validate end date is not in the future
        if ($this->endDate && Carbon::parse($this->endDate)->isFuture()) {
            $this->endDate = now()->toDateString();
        }

        return $this;
    }

    public function forLoan(int $loanId): self
    {
        $this->loanId = $loanId;
        return $this;
    }
}
