<?php

namespace App\Actions\Loans;

use App\Events\CustomerOptedOut;
use App\Exceptions\MtnApiException;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Partner;
use App\Notifications\SmsNotification;
use App\Services\MtnApiService;
use Exception;
use Illuminate\Support\Arr;

class OptOutCustomerAction
{
    public function __construct(protected MtnApiService $mtnApiService)
    {
    }

    /**
     * @throws MtnApiException
     * @throws Exception
     */
    public function execute(string $phoneNumber, string $partnerCode): bool
    {
        $customer = Customer::query()->where('Telephone_Number', $phoneNumber)->first();
        $hasActiveLoans = $customer->loans()->whereNotIn('Credit_Account_Status', [
            Loan::ACCOUNT_STATUS_FULLY_PAID_OFF,
            Loan::ACCOUNT_STATUS_WRITTEN_OFF,
        ])->exists();

        if ($hasActiveLoans) {
            throw new Exception('Customer still has an active loan.');
        }

        // First unlink at MTN
        $optedOutFromProvider = $this->mtnApiService->optOut($customer);

        if (! $optedOutFromProvider) {
            throw new Exception('Unable to opt out the customer.');
        }

        // Then unlink in our system as well or should it be vice versa. todo: inquire about this
        $options = $customer->options;

        if (Arr::has($options, 'savingsaccounts')) {
            unset($options['savingsaccounts']);
            unset($options['loanaccounts']);

            $options['optout_at'] = now()->toDateTimeString();
            $customer->options = $options;
            $customer->save();

            return false;
        }



        // todo: Pass a dynamic partner
        $message = 'You have successfully opted out of the Weekend Loan Service.';
        $partner = Partner::query()->firstWhere('Institution_Code', $partnerCode);
        $customer->notify(new SmsNotification($message, $phoneNumber, $customer->id, $partner->id));

        return true;
    }
}
