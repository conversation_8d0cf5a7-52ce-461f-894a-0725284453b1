<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetPaidOffLoansReportDetailsAction;
use App\Actions\Reports\GetProvisionsReportDetailsAction;
use App\Actions\Reports\GetWrittenOffLoansReportDetailsAction;
use App\Exports\ProvisionsReportExport;
use App\Exports\WrittenOffLoansExport;
use App\Models\LoanProduct;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ProvisionsReport extends Component
{
    use ExportsData, WithPagination;

    public ?int $loanProductId = null;

    public function mount()
    {
//        $this->startDate = now()->startOfMonth()->format('Y-m-d');
       $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.provisions-report', [
            'records' => $this->getReportData(),
            'loanProducts' => LoanProduct::query()->has('loanLossProvisions')->pluck('name', 'id'),
        ]);
    }

    public function printReport()
    {
        $this->validate([
            'loanProductId' => 'required',
        ]);
        $filters = [
            'loanProductId' => $this->loanProductId,
        ];

        $action = app(GetProvisionsReportDetailsAction::class)
            ->filters($filters);

        return app(PdfGeneratorService::class)
            ->view('pdf.provisions-report', [
                'records' => $action->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => [
                    'loanProductName' => LoanProduct::query()->find($this->loanProductId)?->name,
                ],
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $this->validate([
            'loanProductId' => 'required',
        ]);

        return Excel::download(new ProvisionsReportExport($this->loanProductId), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->loanProductId)) {
            return collect([]);
        }
        $action = app(GetProvisionsReportDetailsAction::class)->filters([
            'loanProductId' => $this->loanProductId,
            'endDate' => $this->endDate
        ]);

        return $action->execute();
    }
}
